<template>
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <!-- 返回按钮 -->
        <el-button 
          v-if="showBack"
          type="text" 
          @click="handleBack"
          class="back-button"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        
        <!-- 页面图标 -->
        <div v-if="icon" class="page-icon">
          <el-icon><component :is="icon" /></el-icon>
        </div>
        
        <!-- 标题和描述 -->
        <div class="title-section">
          <h1 class="page-title">{{ title }}</h1>
          <p v-if="description" class="page-description">{{ description }}</p>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb v-if="breadcrumbs.length > 0" class="page-breadcrumb">
            <el-breadcrumb-item 
              v-for="item in breadcrumbs" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
      
      <!-- 右侧操作区域 -->
      <div class="header-right">
        <slot name="actions" />
      </div>
    </div>
    
    <!-- 标签页导航 -->
    <div v-if="tabs.length > 0" class="header-tabs">
      <el-tabs 
        v-model="activeTab" 
        @tab-change="handleTabChange"
        class="page-tabs"
      >
        <el-tab-pane
          v-for="tab in tabs"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        />
      </el-tabs>
    </div>
    
    <!-- 统计信息 -->
    <div v-if="stats.length > 0" class="header-stats">
      <div 
        v-for="stat in stats" 
        :key="stat.key"
        class="stat-item"
      >
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

// 组件属性定义
interface BreadcrumbItem {
  title: string
  path?: string
}

interface TabItem {
  name: string
  label: string
}

interface StatItem {
  key: string
  label: string
  value: string | number
}

interface Props {
  title: string
  description?: string
  icon?: string
  showBack?: boolean
  breadcrumbs?: BreadcrumbItem[]
  tabs?: TabItem[]
  activeTab?: string
  stats?: StatItem[]
}

const props = withDefaults(defineProps<Props>(), {
  showBack: false,
  breadcrumbs: () => [],
  tabs: () => [],
  stats: () => []
})

// 事件定义
const emit = defineEmits<{
  back: []
  tabChange: [tabName: string]
}>()

// 响应式数据
const router = useRouter()

// 方法定义
const handleBack = () => {
  emit('back')
  router.back()
}

const handleTabChange = (tabName: string) => {
  emit('tabChange', tabName)
}
</script>

<style lang="scss" scoped>
.page-header {
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  margin-bottom: 20px;
  overflow: hidden;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    
    .header-left {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      flex: 1;
      
      .back-button {
        margin-top: 4px;
        font-size: 18px;
      }
      
      .page-icon {
        width: 48px;
        height: 48px;
        background: var(--el-color-primary-light-8);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        
        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }
      
      .title-section {
        flex: 1;
        
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0 0 8px 0;
          line-height: 1.2;
        }
        
        .page-description {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          margin: 0 0 12px 0;
          line-height: 1.4;
        }
        
        .page-breadcrumb {
          font-size: 12px;
          
          :deep(.el-breadcrumb__inner) {
            color: var(--el-text-color-secondary);
            
            &.is-link:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      
      .header-left {
        width: 100%;
        
        .title-section {
          .page-title {
            font-size: 20px;
          }
        }
      }
      
      .header-right {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
  
  .header-tabs {
    border-top: 1px solid var(--el-border-color-extra-light);
    
    .page-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        
        .el-tabs__nav-wrap {
          padding: 0 24px;
        }
      }
      
      :deep(.el-tabs__content) {
        display: none;
      }
    }
  }
  
  .header-stats {
    border-top: 1px solid var(--el-border-color-extra-light);
    padding: 16px 24px;
    display: flex;
    gap: 32px;
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: 16px;
      
      .stat-item {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}
</style>
