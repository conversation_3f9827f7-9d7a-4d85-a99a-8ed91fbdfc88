/**
 * 开发环境模式检测和处理工具
 * 用于检测后端是否启用了开发模式，并提供相应的前端处理逻辑
 */

import { request } from './request'
import type { ApiResponse } from '@/types'
import axios from 'axios'

// 开发模式配置接口
interface DevModeConfig {
  enabled: boolean
  authEnabled: boolean
  devMode: boolean
  devUser?: {
    id: string
    username: string
    email: string
    roles: string[]
  }
}

// 开发模式状态
let devModeConfig: DevModeConfig | null = null
let devModeChecked = false

/**
 * 检测后端是否启用开发模式
 * 通过调用健康检查接口获取开发模式状态
 */
export async function detectDevMode(): Promise<DevModeConfig> {
  if (devModeChecked && devModeConfig) {
    return devModeConfig
  }

  try {
    // 调用健康检查接口获取服务状态
    // 🔧 修复：直接访问后端健康检查端点，不通过 /api 前缀
    // 创建一个不带 /api 前缀的 axios 实例用于健康检查
    const healthResponse = await axios.get('/health', {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    const healthData = healthResponse.data

    // 解析开发模式配置
    devModeConfig = {
      enabled: import.meta.env.DEV, // Vite开发环境
      authEnabled: healthData.auth_enabled !== false,
      devMode: healthData.dev_mode === true,
      devUser: healthData.dev_user
    }

    devModeChecked = true
    
    // 🔍 开发环境调试日志
    if (devModeConfig.enabled) {
      console.log('🔧 开发模式检测结果:', devModeConfig)
    }

    return devModeConfig
  } catch (error) {
    console.warn('开发模式检测失败，使用默认配置:', error)
    
    // 默认配置
    devModeConfig = {
      enabled: import.meta.env.DEV,
      authEnabled: true,
      devMode: false
    }
    
    devModeChecked = true
    return devModeConfig
  }
}

/**
 * 检查是否为开发模式
 */
export function isDevMode(): boolean {
  return devModeConfig?.devMode === true || import.meta.env.DEV
}

/**
 * 检查认证是否启用
 */
export function isAuthEnabled(): boolean {
  return devModeConfig?.authEnabled !== false
}

/**
 * 获取开发模式用户信息
 */
export function getDevUser() {
  return devModeConfig?.devUser
}

/**
 * 创建开发模式JWT格式的token
 * 生成一个符合JWT格式的开发模式token，避免解析错误
 */
function createDevModeJWT(username: string): string {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  }

  const payload = {
    sub: 'dev-user-001',
    username: username || '开发者',
    email: 'developer@localhost',
    roles: ['admin', 'developer'],
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 86400, // 24小时后过期
    dev_mode: true
  }

  // 简单的base64编码（开发模式不需要真正的签名）
  const encodedHeader = btoa(JSON.stringify(header))
  const encodedPayload = btoa(JSON.stringify(payload))
  const signature = 'dev-signature-2024'

  return `${encodedHeader}.${encodedPayload}.${signature}`
}

/**
 * 创建开发模式登录响应
 * 当后端启用开发模式时，前端可以跳过实际的登录验证
 */
export function createDevModeLoginResponse(username: string) {
  const devUser = getDevUser() || {
    id: 'dev-user-001',
    username: username || '开发者',
    email: 'developer@localhost',
    roles: ['admin', 'developer']
  }

  // 🔧 生成JWT格式的开发模式token
  const devToken = createDevModeJWT(username)

  return {
    access_token: devToken,
    refresh_token: `${devToken}-refresh`,
    token_type: 'Bearer',
    expires_in: 86400,
    user: {
      id: devUser.id,
      username: devUser.username,
      email: devUser.email,
      displayName: devUser.username,
      avatar: '',
      status: 'active',
      roles: devUser.roles.map(role => ({
        id: `dev-role-${role}`,
        name: role,
        displayName: role,
        description: `开发环境${role}角色`
      })),
      permissions: ['*'], // 开发模式拥有所有权限
      lastLoginAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
}

/**
 * 显示开发模式提示
 */
export function showDevModeWarning() {
  if (isDevMode() && !isAuthEnabled()) {
    console.warn(
      '%c⚠️ 开发模式已启用',
      'color: #ff6b35; font-size: 14px; font-weight: bold;'
    )
    console.warn(
      '%c🔓 用户认证已禁用，登录将使用开发用户身份',
      'color: #ff6b35; font-size: 12px;'
    )
    console.warn(
      '%c🚨 此配置仅适用于开发环境！',
      'color: #ff6b35; font-size: 12px; font-weight: bold;'
    )
  }
}

/**
 * 重置开发模式检测状态
 * 用于强制重新检测开发模式配置
 */
export function resetDevModeDetection() {
  devModeConfig = null
  devModeChecked = false
}

/**
 * 开发模式工具函数
 */
export const devModeUtils = {
  detect: detectDevMode,
  isDevMode,
  isAuthEnabled,
  getDevUser,
  createDevModeLoginResponse,
  showDevModeWarning,
  reset: resetDevModeDetection
}
