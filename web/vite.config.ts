import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 自动导入Vue相关函数
    // 修复 Element Plus 导入配置，解决样式导入问题
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      resolvers: [
        ElementPlusResolver({
          // 禁用自动导入样式，避免路径解析问题
          importStyle: false
        })
      ],
      dts: true,
      eslintrc: {
        enabled: true
      }
    }),
    // 自动导入组件
    Components({
      resolvers: [
        ElementPlusResolver({
          // 禁用自动导入样式，在 main.ts 中手动导入
          importStyle: false
        })
      ],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@types': resolve(__dirname, 'src/types'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    proxy: {
      // 🔧 开发环境API代理配置
      // 代理认证API请求到应用管理服务 (包含认证路由)
      '/api': {
        target: 'http://localhost:8081',  // 修改为应用管理服务端口
        changeOrigin: true,
        secure: false,
        // 🔍 开发环境调试：显示代理请求日志
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`[PROXY] ${req.method} ${req.url} -> ${options.target}${req.url}`)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log(`[PROXY] ${req.method} ${req.url} <- ${proxyRes.statusCode}`)
          })
          proxy.on('error', (err, req, res) => {
            console.error(`[PROXY ERROR] ${req.method} ${req.url}:`, err.message)
          })
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // 将第三方库分离打包
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'utils': ['axios', 'dayjs', 'lodash-es']
        }
      }
    }
  },
  // CSS 预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // 使用现代 Sass API，解决 legacy JS API 弃用警告
        api: 'modern-compiler',
        // 静默弃用警告
        silenceDeprecations: ['legacy-js-api', 'import']
      }
    }
  },
  test: {
    environment: 'jsdom',
    globals: true
  }
})
