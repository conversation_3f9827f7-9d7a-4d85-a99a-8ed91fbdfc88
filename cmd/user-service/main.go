package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"paas-platform/internal/auth"
	"paas-platform/internal/database"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台用户认证服务
// @version 1.0
// @description PaaS 平台的用户认证服务，提供用户管理、认证授权、权限控制等功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8085
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 🚨 显示开发模式警告（如果启用）
	if viper.GetBool("security.jwt.dev_mode") || !viper.GetBool("security.jwt.enabled") {
		logger.Warn("⚠️  用户认证服务开发模式已启用")
		logger.Warn("🔓 用户认证已禁用或使用开发模式")
		logger.Warn("🚨 请确保仅在开发环境使用此配置！")
	}
	
	// 初始化数据库
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化JWT服务
	jwtConfig := auth.JWTConfig{
		Secret:           viper.GetString("security.jwt.secret"),
		AccessTokenTTL:   viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenTTL:  viper.GetDuration("security.jwt.refresh_expires_in"),
		Issuer:           "paas-platform",
		Audience:         "paas-api",
	}
	jwtService := auth.NewJWTService(jwtConfig, logger)
	
	// 初始化认证服务
	authService := auth.NewAuthService(db, jwtService, logger, auth.AuthConfig{
		AccessTokenExpiry:  viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenExpiry: viper.GetDuration("security.jwt.refresh_expires_in"),
		MaxLoginAttempts:   viper.GetInt("security.max_login_attempts"),
		LockoutDuration:    viper.GetDuration("security.lockout_duration"),
	})
	
	// 初始化认证处理器
	authHandler := auth.NewHandler(authService, logger)
	
	// 初始化 Gin 路由
	router := setupRouter(authHandler, jwtService, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler:      router,
		ReadTimeout:  viper.GetDuration("server.read_timeout"),
		WriteTimeout: viper.GetDuration("server.write_timeout"),
		IdleTimeout:  viper.GetDuration("server.idle_timeout"),
	}
	
	// 优雅启动
	go func() {
		logger.Info("用户认证服务启动", 
			"port", viper.GetInt("server.port"),
			"auth_enabled", viper.GetBool("security.jwt.enabled"),
			"dev_mode", viper.GetBool("security.jwt.dev_mode"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭用户认证服务...")
	
	// 优雅关闭，超时时间为 30 秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
	
	logger.Info("用户认证服务已关闭")
}

// initConfig 初始化配置
func initConfig() {
	// 支持通过命令行参数指定配置文件
	configFile := "user-service"
	if len(os.Args) > 1 && os.Args[1] == "--config" && len(os.Args) > 2 {
		configFile = os.Args[2]
	}
	
	viper.SetConfigName(configFile)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")
	
	// 设置默认值
	viper.SetDefault("server.port", 8085)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/user-service.db")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	
	// 🔧 认证相关默认配置
	viper.SetDefault("security.jwt.enabled", true)
	viper.SetDefault("security.jwt.secret", "your-jwt-secret-key")
	viper.SetDefault("security.jwt.expires_in", "24h")
	viper.SetDefault("security.jwt.refresh_expires_in", "168h")
	viper.SetDefault("security.jwt.dev_mode", false)
	viper.SetDefault("security.max_login_attempts", 5)
	viper.SetDefault("security.lockout_duration", "15m")
	
	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("USER_SERVICE")
	
	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	}
	
	log.Printf("使用配置文件: %s", viper.ConfigFileUsed())
}

// setupRouter 设置路由
func setupRouter(authHandler *auth.Handler, jwtService auth.JWTService, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加基础中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":       "healthy",
			"service":      "user-service",
			"version":      "1.0.0",
			"timestamp":    time.Now().Format(time.RFC3339),
			"auth_enabled": viper.GetBool("security.jwt.enabled"),
			"dev_mode":     viper.GetBool("security.jwt.dev_mode"),
			"dev_user": gin.H{
				"id":       viper.GetString("security.jwt.dev_user.id"),
				"username": viper.GetString("security.jwt.dev_user.username"),
				"email":    viper.GetString("security.jwt.dev_user.email"),
				"roles":    viper.GetStringSlice("security.jwt.dev_user.roles"),
			},
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台用户认证服务",
				"version":     "1.0.0",
				"description": "PaaS 平台的用户认证服务，提供用户管理、认证授权、权限控制等功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}
	
	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 🔓 认证相关路由 (无需认证中间件)
		// 这些路由处理登录、注册等不需要预先认证的操作
		authHandler.RegisterRoutes(v1)
		
		// 🔒 需要认证的路由组
		// 这些路由需要有效的JWT令牌才能访问
		authenticated := v1.Group("")
		// 创建 JWT 服务适配器以兼容 middleware.JWTService 接口
		jwtAdapter := auth.NewJWTServiceAdapter(jwtService, logger)
		authenticated.Use(middleware.NewAuthMiddleware(jwtAdapter, logger).Handler())
		{
			// 用户管理相关的认证路由
			// TODO: 实现 RegisterAuthenticatedRoutes 方法或使用其他方法注册认证路由
			// authHandler.RegisterAuthenticatedRoutes(authenticated)
		}
	}
	
	return router
}
