package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"paas-platform/internal/auth"
	"paas-platform/internal/database"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台API网关
// @version 1.0
// @description PaaS 平台的统一API网关，提供认证、路由转发和API聚合功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 🚨 显示开发模式警告（如果启用）
	if viper.GetBool("security.jwt.dev_mode") || !viper.GetBool("security.jwt.enabled") {
		logger.Warn("⚠️  API网关开发模式已启用")
		logger.Warn("🔓 用户认证已禁用或使用开发模式")
		logger.Warn("🚨 请确保仅在开发环境使用此配置！")
	}
	
	// 初始化数据库（用于认证服务）
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化JWT服务
	jwtConfig := auth.JWTConfig{
		Secret:           viper.GetString("security.jwt.secret"),
		AccessTokenTTL:   viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenTTL:  viper.GetDuration("security.jwt.refresh_expires_in"),
		Issuer:           "paas-platform",
		Audience:         "paas-api",
	}
	jwtService := auth.NewJWTService(jwtConfig, logger)
	
	// 初始化认证服务
	authService := auth.NewAuthService(db, jwtService, logger, auth.AuthConfig{
		AccessTokenExpiry:  viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenExpiry: viper.GetDuration("security.jwt.refresh_expires_in"),
		MaxLoginAttempts:   5,
		LockoutDuration:    15 * time.Minute,
	})
	
	// 初始化认证处理器
	authHandler := auth.NewHandler(authService, logger)
	
	// 初始化 Gin 路由
	router := setupRouter(authHandler, jwtService, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler:      router,
		ReadTimeout:  viper.GetDuration("server.read_timeout"),
		WriteTimeout: viper.GetDuration("server.write_timeout"),
		IdleTimeout:  viper.GetDuration("server.idle_timeout"),
	}
	
	// 优雅启动
	go func() {
		logger.Info("API网关启动", 
			"port", viper.GetInt("server.port"),
			"auth_enabled", viper.GetBool("security.jwt.enabled"),
			"dev_mode", viper.GetBool("security.jwt.dev_mode"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭API网关...")
	
	// 优雅关闭，超时时间为 30 秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
	
	logger.Info("API网关已关闭")
}

// initConfig 初始化配置
func initConfig() {
	// 支持通过命令行参数指定配置文件
	configFile := "api-gateway"
	if len(os.Args) > 1 && os.Args[1] == "--config" && len(os.Args) > 2 {
		configFile = os.Args[2]
	}
	
	viper.SetConfigName(configFile)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")
	
	// 设置默认值
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/api-gateway.db")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	
	// 🔧 认证相关默认配置
	viper.SetDefault("security.jwt.enabled", true)
	viper.SetDefault("security.jwt.secret", "your-jwt-secret-key")
	viper.SetDefault("security.jwt.expires_in", "24h")
	viper.SetDefault("security.jwt.refresh_expires_in", "168h")
	viper.SetDefault("security.jwt.dev_mode", false)
	viper.SetDefault("security.jwt.dev_token", "dev-token")
	
	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("API_GATEWAY")
	
	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	}
	
	log.Printf("使用配置文件: %s", viper.ConfigFileUsed())
}

// setupRouter 设置路由
func setupRouter(authHandler *auth.Handler, jwtService auth.JWTService, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加基础中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":      "healthy",
			"service":     "api-gateway",
			"version":     "1.0.0",
			"timestamp":   time.Now().Format(time.RFC3339),
			"auth_enabled": viper.GetBool("security.jwt.enabled"),
			"dev_mode":    viper.GetBool("security.jwt.dev_mode"),
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台API网关",
				"version":     "1.0.0",
				"description": "PaaS 平台的统一API网关，提供认证、路由转发和API聚合功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}
	
	// 🔀 API代理路由 - 将所有API请求代理到对应的微服务
	api := router.Group("/api")
	{
		// 🔐 认证相关路由 -> User Service (无需预先认证)
		authGroup := api.Group("/v1/auth")
		{
			authGroup.Any("/*path", func(c *gin.Context) {
				// 代理认证请求到User Service
				targetPath := "/api/v1/auth" + c.Param("path")
				targetURL := "http://localhost:8085" + targetPath

				logger.Debug("代理认证请求到User Service",
					"method", c.Request.Method,
					"source_path", c.Request.URL.Path,
					"target_url", targetURL)

				proxyToURL(c, targetURL, logger)
			})
		}

		// 🔒 业务API路由组 (需要认证，在开发模式下会跳过)
		v1 := api.Group("/v1")
		// 创建 JWT 服务适配器以兼容 middleware.JWTService 接口
		jwtAdapter := auth.NewJWTServiceAdapter(jwtService, logger)
		v1.Use(middleware.NewAuthMiddleware(jwtAdapter, logger).Handler())
		{
			// 代理到其他服务的路由
			setupProxyRoutes(v1, logger)
		}
	}
	
	return router
}

// setupProxyRoutes 设置代理路由
func setupProxyRoutes(router *gin.RouterGroup, logger logger.Logger) {
	// 🔧 开发环境提示：在开发模式下，这些路由的认证会被自动跳过

	// 应用管理服务代理 - /api/v1/apps/* -> http://localhost:8081/api/v1/apps/*
	apps := router.Group("/apps")
	{
		apps.Any("/*path", func(c *gin.Context) {
			// 构建目标URL：保持完整路径
			targetPath := "/api/v1/apps" + c.Param("path")
			targetURL := "http://localhost:8081" + targetPath

			// 记录代理请求
			logger.Debug("代理应用管理请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			// 执行代理
			proxyToURL(c, targetURL, logger)
		})
	}

	// 脚本执行服务代理 - /api/v1/scripts/* -> http://localhost:8084/api/v1/scripts/*
	scripts := router.Group("/scripts")
	{
		scripts.Any("/*path", func(c *gin.Context) {
			targetPath := "/api/v1/scripts" + c.Param("path")
			targetURL := "http://localhost:8084" + targetPath

			logger.Debug("代理脚本服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// CI/CD服务代理 - /api/v1/cicd/* -> http://localhost:8082/api/v1/*
	cicd := router.Group("/cicd")
	{
		cicd.Any("/*path", func(c *gin.Context) {
			// CI/CD服务的路径映射稍有不同
			targetPath := "/api/v1" + c.Param("path")
			targetURL := "http://localhost:8082" + targetPath

			logger.Debug("代理CI/CD服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// 配置服务代理 - /api/v1/configs/* -> http://localhost:8083/api/v1/configs/*
	configs := router.Group("/configs")
	{
		configs.Any("/*path", func(c *gin.Context) {
			targetPath := "/api/v1/configs" + c.Param("path")
			targetURL := "http://localhost:8083" + targetPath

			logger.Debug("代理配置服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}
}

// proxyToURL 简化的代理函数
func proxyToURL(c *gin.Context, targetURL string, logger logger.Logger) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 读取请求体
	var bodyBytes []byte
	if c.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(c.Request.Body)
	}

	// 创建代理请求
	req, err := http.NewRequest(c.Request.Method, targetURL, bytes.NewReader(bodyBytes))
	if err != nil {
		logger.Error("创建代理请求失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "代理请求创建失败",
		})
		return
	}

	// 复制请求头
	for key, values := range c.Request.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 设置代理头部
	req.Header.Set("X-Forwarded-For", c.ClientIP())
	req.Header.Set("X-Real-IP", c.ClientIP())

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("代理请求执行失败", "error", err, "target_url", targetURL)
		c.JSON(http.StatusBadGateway, gin.H{
			"error":   "服务不可用",
			"message": "无法连接到目标服务",
			"target":  targetURL,
		})
		return
	}
	defer resp.Body.Close()

	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// 设置状态码并复制响应体
	c.Status(resp.StatusCode)
	io.Copy(c.Writer, resp.Body)
}
